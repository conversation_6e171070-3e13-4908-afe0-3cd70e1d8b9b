namespace ZeroDateStrat.Models;

public class MarketRegime
{
    public decimal Vix { get; set; }
    public VolatilityRegime VolatilityRegime { get; set; }
    public MarketTrend Trend { get; set; }
    public string OverallRegime { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public decimal Confidence { get; set; }
    public DateTime Timestamp { get; set; }
}

public enum VolatilityRegime
{
    Unknown,
    Low,
    Medium,
    High
}

public enum MarketTrend
{
    Unknown,
    Bullish,
    Bearish,
    Neutral
}

public class VolatilityForecast
{
    public string Symbol { get; set; } = string.Empty;
    public DateTime ForecastDate { get; set; }
    public int DaysAhead { get; set; }
    public decimal CurrentVolatility { get; set; }
    public decimal ForecastedVolatility { get; set; }
    public decimal PredictedVolatility { get; set; }
    public decimal VolatilityTrend { get; set; } // Positive = increasing, Negative = decreasing
    public decimal Confidence { get; set; }
    public string Model { get; set; } = string.Empty; // GARCH, Historical, etc.
    public Dictionary<string, decimal> ModelParameters { get; set; } = new();
    public List<decimal> VolatilityPath { get; set; } = new(); // Daily forecasts
}

public class MarketMicrostructure
{
    public string Symbol { get; set; } = string.Empty;
    public DateTime Timestamp { get; set; }
    public decimal BidAskSpread { get; set; }
    public decimal VolumeWeightedAveragePrice { get; set; }
    public decimal OrderFlowImbalance { get; set; }
    public decimal MarketImpact { get; set; }
    public decimal LiquidityScore { get; set; }
    public decimal VolatilitySkew { get; set; }
    public decimal MomentumScore { get; set; }
    public Dictionary<string, decimal> TechnicalIndicators { get; set; } = new();
}

public class RegimeTransitionProbability
{
    public DateTime AnalysisDate { get; set; }
    public VolatilityRegime CurrentRegime { get; set; }
    public Dictionary<VolatilityRegime, decimal> TransitionProbabilities { get; set; } = new();
    public decimal RegimeStability { get; set; } // How stable is current regime
    public int DaysInCurrentRegime { get; set; }
    public decimal TransitionSignal { get; set; } // Strength of transition signal
}

public class MarketStressIndicators
{
    public DateTime Timestamp { get; set; }
    public decimal VixLevel { get; set; }
    public decimal VixTerm { get; set; } // VIX9D/VIX ratio
    public decimal SkewIndex { get; set; }
    public decimal CreditSpreads { get; set; }
    public decimal DollarIndex { get; set; }
    public decimal YieldCurveSlope { get; set; }
    public decimal MarketBreadth { get; set; }
    public decimal StressLevel { get; set; } // 0-100 composite stress score
    public List<string> StressFactors { get; set; } = new();
}

public class VolatilitySurface
{
    public string Symbol { get; set; } = string.Empty;
    public DateTime Timestamp { get; set; }
    public Dictionary<decimal, Dictionary<int, decimal>> Surface { get; set; } = new(); // Strike -> DTE -> IV
    public decimal AtmVolatility { get; set; }
    public decimal VolatilitySkew { get; set; }
    public decimal VolatilitySmile { get; set; }
    public decimal TermStructureSlope { get; set; }
}

public class MultiTimeframeAnalysis
{
    public string Symbol { get; set; } = string.Empty;
    public DateTime Timestamp { get; set; }
    public Dictionary<string, TrendAnalysis> TimeframeTrends { get; set; } = new(); // "1m", "5m", "15m", "1h", "1d"
    public Dictionary<string, TechnicalIndicators> TimeframeIndicators { get; set; } = new();
    public decimal OverallMomentum { get; set; }
    public decimal TrendAlignment { get; set; } // How aligned are different timeframes
    public string DominantTimeframe { get; set; } = string.Empty;
    public decimal SignalStrength { get; set; }
    public string OverallTrend { get; set; } = string.Empty;
    public decimal TrendStrength { get; set; }
    public int ConflictingSignals { get; set; }
    public decimal ConfidenceScore { get; set; }
}

public class TrendAnalysis
{
    public MarketTrend Trend { get; set; }
    public decimal Strength { get; set; }
    public decimal Momentum { get; set; }
    public decimal Support { get; set; }
    public decimal Resistance { get; set; }
    public Dictionary<string, decimal> Indicators { get; set; } = new();
}

public class SignalValidationResult
{
    public string SignalId { get; set; } = string.Empty;
    public bool IsValid { get; set; }
    public decimal QualityScore { get; set; }
    public List<string> Issues { get; set; } = new();
    public List<string> Warnings { get; set; } = new();
    public DateTime ValidationTimestamp { get; set; }
}

// Phase 1 Enhanced Market Analysis Models
public class MarketSentimentAnalysis
{
    public string Symbol { get; set; } = string.Empty;
    public DateTime Timestamp { get; set; }
    public decimal PutCallRatio { get; set; }
    public decimal PutCallRatioMA { get; set; } // Moving average for comparison
    public decimal OptionsFlowSentiment { get; set; } // -1 to 1 scale
    public decimal VixSentiment { get; set; } // Based on VIX term structure
    public decimal MarketBreadthSentiment { get; set; }
    public decimal CompositeSentimentScore { get; set; } // Overall sentiment -100 to 100
    public SentimentRegime SentimentRegime { get; set; }
    public List<string> SentimentFactors { get; set; } = new();
    public Dictionary<string, decimal> SentimentComponents { get; set; } = new();
}

public class OptionsFlowAnalysis
{
    public string Symbol { get; set; } = string.Empty;
    public DateTime Timestamp { get; set; }
    public decimal CallVolume { get; set; }
    public decimal PutVolume { get; set; }
    public decimal CallOpenInterest { get; set; }
    public decimal PutOpenInterest { get; set; }
    public decimal UnusualActivityScore { get; set; } // 0-100
    public decimal InstitutionalFlowScore { get; set; } // -100 to 100
    public decimal DarkPoolFlow { get; set; }
    public decimal OptionsFlowMomentum { get; set; }
    public List<UnusualOptionsActivity> UnusualActivity { get; set; } = new();
    public Dictionary<string, decimal> FlowMetrics { get; set; } = new();
}

public class VolatilityRegimeDetection
{
    public string Symbol { get; set; } = string.Empty;
    public DateTime Timestamp { get; set; }
    public VolatilityRegime CurrentRegime { get; set; }
    public VolatilityRegime PredictedRegime { get; set; }
    public decimal RegimeConfidence { get; set; }
    public decimal VolatilityClusteringScore { get; set; }
    public decimal GarchVolatility { get; set; }
    public decimal VolatilityBreakoutProbability { get; set; }
    public List<VolatilityRegimeSignal> RegimeSignals { get; set; } = new();
    public Dictionary<string, decimal> VolatilityMetrics { get; set; } = new();
}

public class MarketBreadthIndicators
{
    public DateTime Timestamp { get; set; }
    public decimal AdvanceDeclineRatio { get; set; }
    public decimal NewHighsNewLows { get; set; }
    public decimal UpDownVolumeRatio { get; set; }
    public decimal UpVolumeDownVolume { get; set; }
    public decimal McClellanOscillator { get; set; }
    public decimal BreadthThrust { get; set; }
    public decimal SectorRotationScore { get; set; }
    public BreadthRegime BreadthRegime { get; set; }
    public List<string> BreadthSignals { get; set; } = new();
}

public class CorrelationBreakdownAlert
{
    public DateTime Timestamp { get; set; }
    public List<string> Symbols { get; set; } = new();
    public bool IsBreakdownDetected { get; set; }
    public decimal CorrelationChange { get; set; }
    public decimal AlertSeverity { get; set; } // 0-100
    public List<CorrelationPair> BreakdownPairs { get; set; } = new();
    public string AlertMessage { get; set; } = string.Empty;
}

public class VolatilitySpikeAlert
{
    public string Symbol { get; set; } = string.Empty;
    public DateTime Timestamp { get; set; }
    public bool IsSpikeDetected { get; set; }
    public decimal CurrentVolatility { get; set; }
    public decimal NormalVolatility { get; set; }
    public decimal SpikeIntensity { get; set; } // Ratio of current to normal
    public VolatilitySpikeType SpikeType { get; set; }
    public string AlertMessage { get; set; } = string.Empty;
}

public class UnusualOptionsActivity
{
    public string Symbol { get; set; } = string.Empty;
    public DateTime Timestamp { get; set; }
    public OptionType OptionType { get; set; }
    public decimal StrikePrice { get; set; }
    public DateTime ExpirationDate { get; set; }
    public decimal Volume { get; set; }
    public decimal AverageVolume { get; set; }
    public decimal VolumeRatio { get; set; } // Current/Average
    public decimal UnusualityScore { get; set; } // 0-100
    public ActivityType ActivityType { get; set; }
    public string Description { get; set; } = string.Empty;
}

// Supporting Enums and Classes
public enum SentimentRegime
{
    ExtremeFear,
    Fear,
    Neutral,
    Greed,
    ExtremeGreed
}

public enum BreadthRegime
{
    Deteriorating,
    Weak,
    Neutral,
    Strong,
    Expanding
}

public enum VolatilitySpikeType
{
    None,
    Mild,
    Moderate,
    Severe,
    Extreme,
    Intraday,
    Overnight,
    EventDriven,
    TechnicalBreakout
}

public enum ActivityType
{
    Unknown,
    Sweep,
    Block,
    Split,
    Unusual
}

public class CorrelationPair
{
    public string Symbol1 { get; set; } = string.Empty;
    public string Symbol2 { get; set; } = string.Empty;
    public decimal CurrentCorrelation { get; set; }
    public decimal HistoricalCorrelation { get; set; }
    public decimal PreviousCorrelation { get; set; }
    public decimal CorrelationChange { get; set; }
    public DateTime CalculationDate { get; set; }
}

public class VolatilityRegimeSignal
{
    public DateTime Timestamp { get; set; }
    public VolatilityRegime FromRegime { get; set; }
    public VolatilityRegime ToRegime { get; set; }
    public string SignalType { get; set; } = string.Empty;
    public decimal Strength { get; set; }
    public decimal SignalStrength { get; set; }
    public string Description { get; set; } = string.Empty;
}

// Additional models for Phase 3
public class HistoricalDataPoint
{
    public string Symbol { get; set; } = string.Empty;
    public DateTime Date { get; set; }
    public decimal Price { get; set; }
    public long Volume { get; set; }
    public decimal High { get; set; }
    public decimal Low { get; set; }
    public decimal Open { get; set; }
}

public class VixDataPoint
{
    public DateTime Date { get; set; }
    public decimal Value { get; set; }
    public decimal High { get; set; }
    public decimal Low { get; set; }
    public long Volume { get; set; }
}

// Synthetic VIX Models
public class SyntheticVixConfiguration
{
    public string Note { get; set; } = "Direct access to VIX (CBOE index) is not available.";
    public List<SyntheticVixComponent> SubstituteIndices { get; set; } = new();
    public SyntheticVixNormalization Normalization { get; set; } = new();
    public string CompositeIndexLabel { get; set; } = "SyntheticVIX";
    public string Usage { get; set; } = "Use 'SyntheticVIX' in place of 'VIX' for volatility regime checks, signal thresholds, and entry/exit filters.";
}

public class SyntheticVixComponent
{
    public string Symbol { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public decimal Weight { get; set; }
    public string Source { get; set; } = string.Empty;
    public decimal CurrentPrice { get; set; }
    public DateTime LastUpdate { get; set; }
}

public class SyntheticVixNormalization
{
    public string Method { get; set; } = "z-score";
    public int Window { get; set; } = 20;
}

public class SyntheticVixCalculation
{
    public string Symbol { get; set; } = string.Empty;
    public decimal Price { get; set; }
    public decimal NormalizedValue { get; set; }
    public decimal Weight { get; set; }
    public decimal Confidence { get; set; }
    public DateTime Timestamp { get; set; }
}

public class SyntheticVixHistoricalData
{
    public DateTime Timestamp { get; set; }
    public decimal RawValue { get; set; }
    public decimal NormalizedValue { get; set; }
    public Dictionary<string, decimal> ComponentValues { get; set; } = new();
}

public class SyntheticVixAnalysis
{
    public decimal CurrentLevel { get; set; }
    public decimal ZScore { get; set; }
    public string Interpretation { get; set; } = string.Empty;
    public string RiskLevel { get; set; } = string.Empty;
    public string TradingRecommendation { get; set; } = string.Empty;
    public decimal PositionSizeMultiplier { get; set; }
    public List<SyntheticVixCalculation> ComponentBreakdown { get; set; } = new();
    public DateTime Timestamp { get; set; }
}

// SyntheticVIX Analytics Models
public class SyntheticVixHealthReport
{
    public DateTime Timestamp { get; set; }
    public HealthStatus OverallHealth { get; set; }
    public string Summary { get; set; } = string.Empty;
    public List<string> Issues { get; set; } = new();
    public Dictionary<string, ComponentHealthStatus> ComponentHealth { get; set; } = new();
    public bool AccuracyValidation { get; set; }
}

public class ComponentHealthStatus
{
    public string Symbol { get; set; } = string.Empty;
    public bool IsHealthy { get; set; }
    public decimal LastPrice { get; set; }
    public int RecentFailureCount { get; set; }
    public DateTime LastSuccessfulUpdate { get; set; }
    public string ErrorMessage { get; set; } = string.Empty;
}

public class SyntheticVixPerformanceMetrics
{
    public DateTime Timestamp { get; set; }
    public double AverageCalculationTime { get; set; }
    public int CalculationFrequency { get; set; }
    public decimal AverageZScore { get; set; }
    public decimal ValueStability { get; set; }
    public Dictionary<string, ComponentPerformanceMetrics> ComponentMetrics { get; set; } = new();
    public Dictionary<string, decimal> ComponentContributions { get; set; } = new();
}

public class ComponentPerformanceMetrics
{
    public string Symbol { get; set; } = string.Empty;
    public long TotalCalculations { get; set; }
    public decimal AveragePrice { get; set; }
    public decimal AverageConfidence { get; set; }
    public DateTime LastUpdate { get; set; }
}

public class SyntheticVixAlert
{
    public string Id { get; set; } = string.Empty;
    public string Type { get; set; } = string.Empty;
    public AlertSeverity Severity { get; set; }
    public string Message { get; set; } = string.Empty;
    public DateTime Timestamp { get; set; }
    public bool IsAcknowledged { get; set; }
}



public class SyntheticVixCorrelationAnalysis
{
    public DateTime Timestamp { get; set; }
    public Dictionary<string, decimal> ComponentCorrelations { get; set; } = new();
    public decimal OverallCorrelation { get; set; }
    public decimal CorrelationStability { get; set; }
}

public class SyntheticVixCalibrationReport
{
    public DateTime Timestamp { get; set; }
    public ZScoreDistribution ZScoreDistribution { get; set; } = new();
    public List<CalibrationAdjustment> RecommendedAdjustments { get; set; } = new();
}

public class ZScoreDistribution
{
    public decimal Mean { get; set; }
    public decimal StandardDeviation { get; set; }
    public decimal SkewnessFactor { get; set; }
}

public class CalibrationAdjustment
{
    public string Type { get; set; } = string.Empty;
    public string Description { get; set; } = string.Empty;
    public string Severity { get; set; } = string.Empty;
}

public class ComponentFailure
{
    public string Symbol { get; set; } = string.Empty;
    public DateTime Timestamp { get; set; }
    public string ErrorMessage { get; set; } = string.Empty;
    public string ExceptionType { get; set; } = string.Empty;
}

public enum HealthStatus
{
    Healthy,
    Degraded,
    Unhealthy
}

public class TrainingData
{
    public string Id { get; set; } = Guid.NewGuid().ToString();
    public Dictionary<string, decimal> Features { get; set; } = new();
    public decimal Target { get; set; }
    public DateTime Timestamp { get; set; }
    public string Label { get; set; } = string.Empty;
}

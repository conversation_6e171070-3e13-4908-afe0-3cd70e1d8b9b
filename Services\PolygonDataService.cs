using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System.Text;
using ZeroDateStrat.Models;

namespace ZeroDateStrat.Services;

public interface IPolygonDataService
{
    Task<decimal> GetCurrentVixAsync();
    Task<decimal> GetVixChangeAsync(TimeSpan period);
    Task<List<VixDataPoint>> GetVixHistoryAsync(DateTime startDate, DateTime endDate);

    /// <summary>
    /// Gets current quote for a symbol. For indices (symbols starting with "I:" like I:SPX, I:VIX),
    /// returns last trade price as both bid and ask since indices don't have bid/ask quotes.
    /// For tradeable securities, returns actual bid/ask quotes.
    /// </summary>
    Task<PolygonQuote> GetCurrentQuoteAsync(string symbol);

    /// <summary>
    /// Alias for GetCurrentQuoteAsync - same behavior for indices vs tradeable securities
    /// </summary>
    Task<PolygonQuote> GetQuoteAsync(string symbol);
    Task<bool> TestConnectionAsync();

    // Options data methods
    Task<List<PolygonOptionsContract>> GetOptionsContractsAsync(string underlyingSymbol, DateTime? expirationDate = null);
    Task<List<PolygonOptionsContract>> GetOptionsContractsForDateAsync(string underlyingSymbol, DateTime expirationDate);
    Task<decimal> GetHistoricalPriceAsync(string symbol, DateTime date);
    Task<bool> IsMarketOpenAsync();

    // Enhanced options pricing methods for Options Starter subscription
    Task<PolygonOptionsChainSnapshot> GetOptionsChainSnapshotAsync(string underlyingSymbol, DateTime? expirationDate = null, decimal? strikePrice = null);
    Task<PolygonOptionSnapshot> GetOptionSnapshotAsync(string optionTicker);
    Task<List<PolygonOptionQuote>> GetOptionQuotesAsync(string optionTicker, DateTime? date = null);
    Task<List<PolygonOptionTrade>> GetOptionTradesAsync(string optionTicker, DateTime? date = null);
    Task<PolygonOptionAggregates> GetOptionAggregatesAsync(string optionTicker, DateTime fromDate, DateTime toDate, string timespan = "minute");
}

public class PolygonDataService : IPolygonDataService
{
    private readonly IConfiguration _configuration;
    private readonly ILogger<PolygonDataService> _logger;
    private readonly HttpClient _httpClient;
    private readonly ISecurityService _securityService;
    private string? _apiKey;
    private readonly string _baseUrl = "https://api.polygon.io";

    // Cache for VIX data to avoid excessive API calls
    private decimal _cachedVix = 0;
    private DateTime _lastVixUpdate = DateTime.MinValue;
    private readonly TimeSpan _cacheExpiry = TimeSpan.FromMinutes(1); // Cache for 1 minute

    public PolygonDataService(IConfiguration configuration, ILogger<PolygonDataService> logger, HttpClient httpClient, ISecurityService securityService)
    {
        _configuration = configuration;
        _logger = logger;
        _httpClient = httpClient;
        _securityService = securityService;

        // Configure HttpClient
        _httpClient.BaseAddress = new Uri(_baseUrl);
        _httpClient.DefaultRequestHeaders.Add("User-Agent", "ZeroDateStrat/1.0");
    }

    private async Task<string> GetSecurePolygonApiKeyAsync()
    {
        if (_apiKey != null)
            return _apiKey;

        var configKey = _configuration["Polygon:ApiKey"];
        if (string.IsNullOrEmpty(configKey))
            throw new InvalidOperationException("Polygon API key not configured");

        // Check if the key is encrypted (starts with encrypted prefix)
        if (configKey.StartsWith("ENC:"))
        {
            var encryptedKey = configKey.Substring(4);
            _apiKey = await _securityService.DecryptSensitiveDataAsync(encryptedKey);
        }
        else
        {
            _apiKey = configKey;
        }

        return _apiKey;
    }

    public async Task<decimal> GetCurrentVixAsync()
    {
        try
        {
            // Check cache first
            if (_cachedVix > 0 && DateTime.UtcNow - _lastVixUpdate < _cacheExpiry)
            {
                _logger.LogDebug($"Returning cached VIX: {_cachedVix:F2}");
                return _cachedVix;
            }

            _logger.LogDebug("Fetching current VIX from Polygon.io");

            // Get current VIX quote
            var apiKey = await GetSecurePolygonApiKeyAsync();
            var url = $"/v2/last/trade/I:VIX?apikey={apiKey}";
            var response = await _httpClient.GetAsync(url);

            if (!response.IsSuccessStatusCode)
            {
                _logger.LogWarning($"Polygon API returned {response.StatusCode} for VIX request");
                return await GetFallbackVixAsync();
            }

            var content = await response.Content.ReadAsStringAsync();
            var vixResponse = JsonConvert.DeserializeObject<PolygonVixResponse>(content);

            if (vixResponse?.Results?.Price > 0)
            {
                _cachedVix = (decimal)vixResponse.Results.Price;
                _lastVixUpdate = DateTime.UtcNow;
                
                _logger.LogInformation($"Current VIX from Polygon: {_cachedVix:F2}");
                return _cachedVix;
            }

            _logger.LogWarning("Invalid VIX data received from Polygon");
            return await GetFallbackVixAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching VIX from Polygon.io");
            return await GetFallbackVixAsync();
        }
    }

    public async Task<decimal> GetVixChangeAsync(TimeSpan period)
    {
        try
        {
            var currentVix = await GetCurrentVixAsync();
            var endDate = DateTime.UtcNow.Date;
            var startDate = endDate.Subtract(period);

            var history = await GetVixHistoryAsync(startDate, endDate);
            
            if (history.Any())
            {
                var previousVix = history.First().Value;
                var change = currentVix - previousVix;
                
                _logger.LogDebug($"VIX change over {period}: {change:F2} (from {previousVix:F2} to {currentVix:F2})");
                return change;
            }

            return 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error calculating VIX change over {period}");
            return 0;
        }
    }

    public async Task<List<VixDataPoint>> GetVixHistoryAsync(DateTime startDate, DateTime endDate)
    {
        return await GetVixHistoryAsync(startDate, endDate, "1/day");
    }

    /// <summary>
    /// Gets VIX historical data with configurable time intervals
    /// Supports: 1/minute, 5/minute, 15/minute, 30/minute, 1/hour, 1/day
    /// </summary>
    public async Task<List<VixDataPoint>> GetVixHistoryAsync(DateTime startDate, DateTime endDate, string timespan)
    {
        try
        {
            _logger.LogDebug($"Fetching VIX history from {startDate:yyyy-MM-dd} to {endDate:yyyy-MM-dd} with {timespan} intervals");

            var startDateStr = startDate.ToString("yyyy-MM-dd");
            var endDateStr = endDate.ToString("yyyy-MM-dd");

            var apiKey = await GetSecurePolygonApiKeyAsync();
            var url = $"/v2/aggs/ticker/I:VIX/range/{timespan}/{startDateStr}/{endDateStr}?adjusted=true&sort=desc&limit=50000&apikey={apiKey}";
            var response = await _httpClient.GetAsync(url);

            if (!response.IsSuccessStatusCode)
            {
                _logger.LogWarning($"Polygon API returned {response.StatusCode} for VIX history request");
                return new List<VixDataPoint>();
            }

            var content = await response.Content.ReadAsStringAsync();
            var historyResponse = JsonConvert.DeserializeObject<PolygonHistoryResponse>(content);

            if (historyResponse?.Results != null)
            {
                var vixHistory = historyResponse.Results.Select(r => new VixDataPoint
                {
                    Date = DateTimeOffset.FromUnixTimeMilliseconds(r.Timestamp).DateTime,
                    Value = (decimal)r.Close,
                    High = (decimal)r.High,
                    Low = (decimal)r.Low,
                    Volume = r.Volume
                }).OrderBy(v => v.Date).ToList();

                _logger.LogDebug($"Retrieved {vixHistory.Count} VIX data points");
                return vixHistory;
            }

            return new List<VixDataPoint>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fetching VIX history from Polygon.io");
            return new List<VixDataPoint>();
        }
    }

    public async Task<PolygonQuote> GetCurrentQuoteAsync(string symbol)
    {
        try
        {
            _logger.LogDebug($"Fetching current quote for {symbol}");

            // Check if this is an index symbol (indices don't have bid/ask quotes)
            if (IsIndexSymbol(symbol))
            {
                _logger.LogDebug($"{symbol} is an index - getting last trade price instead of bid/ask");
                return await GetIndexPriceAsQuoteAsync(symbol);
            }

            var apiKey = await GetSecurePolygonApiKeyAsync();
            var url = $"/v2/last/nbbo/{symbol}?apikey={apiKey}";
            var response = await _httpClient.GetAsync(url);

            if (!response.IsSuccessStatusCode)
            {
                _logger.LogWarning($"Polygon API returned {response.StatusCode} for {symbol} quote");
                return new PolygonQuote { Symbol = symbol };
            }

            var content = await response.Content.ReadAsStringAsync();
            var quoteResponse = JsonConvert.DeserializeObject<PolygonQuoteResponse>(content);

            if (quoteResponse?.Results != null)
            {
                return new PolygonQuote
                {
                    Symbol = symbol,
                    Bid = (decimal)quoteResponse.Results.Bid,
                    Ask = (decimal)quoteResponse.Results.Ask,
                    BidSize = quoteResponse.Results.BidSize,
                    AskSize = quoteResponse.Results.AskSize,
                    Timestamp = DateTimeOffset.FromUnixTimeMilliseconds(quoteResponse.Results.Timestamp).DateTime
                };
            }

            return new PolygonQuote { Symbol = symbol };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error fetching quote for {symbol}");
            return new PolygonQuote { Symbol = symbol };
        }
    }

    public async Task<PolygonQuote> GetQuoteAsync(string symbol)
    {
        // Alias for GetCurrentQuoteAsync to satisfy interface requirements
        return await GetCurrentQuoteAsync(symbol);
    }

    public async Task<bool> TestConnectionAsync()
    {
        try
        {
            _logger.LogInformation("Testing Polygon.io connection...");

            // Try the simplest possible endpoint - market status
            var apiKey = await GetSecurePolygonApiKeyAsync();
            var url = $"/v1/marketstatus/now?apikey={apiKey}";
            var response = await _httpClient.GetAsync(url);

            if (response.IsSuccessStatusCode)
            {
                _logger.LogInformation("Polygon.io connection test successful");
                return true;
            }

            // Log more details about the failure
            var content = await response.Content.ReadAsStringAsync();
            _logger.LogWarning($"Polygon.io connection test failed: {response.StatusCode}");
            _logger.LogDebug($"Response content: {content}");

            // If market status fails, try a basic stock quote
            _logger.LogInformation("Testing basic stock quote endpoint...");
            var stockUrl = $"/v2/last/trade/AAPL?apikey={apiKey}";
            var stockResponse = await _httpClient.GetAsync(stockUrl);

            if (stockResponse.IsSuccessStatusCode)
            {
                _logger.LogInformation("Stock quote endpoint accessible - connection is working");
                return true;
            }
            else
            {
                var stockContent = await stockResponse.Content.ReadAsStringAsync();
                _logger.LogWarning($"Stock quote endpoint also failed: {stockResponse.StatusCode}");
                _logger.LogDebug($"Stock response content: {stockContent}");
            }

            return false;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Polygon.io connection test failed");
            return false;
        }
    }

    public async Task<List<PolygonOptionsContract>> GetOptionsContractsAsync(string underlyingSymbol, DateTime? expirationDate = null)
    {
        try
        {
            _logger.LogDebug($"Fetching options contracts for {underlyingSymbol}");

            var apiKey = await GetSecurePolygonApiKeyAsync();
            var url = $"/v3/reference/options/contracts?underlying_ticker={underlyingSymbol}&limit=1000&apikey={apiKey}";

            if (expirationDate.HasValue)
            {
                var expDateStr = expirationDate.Value.ToString("yyyy-MM-dd");
                url += $"&expiration_date={expDateStr}";
            }

            var response = await _httpClient.GetAsync(url);

            if (!response.IsSuccessStatusCode)
            {
                _logger.LogWarning($"Polygon API returned {response.StatusCode} for options contracts request");
                return new List<PolygonOptionsContract>();
            }

            var content = await response.Content.ReadAsStringAsync();
            var optionsResponse = JsonConvert.DeserializeObject<PolygonOptionsContractsResponse>(content);

            if (optionsResponse?.Results != null)
            {
                _logger.LogInformation($"Retrieved {optionsResponse.Results.Count} options contracts for {underlyingSymbol}");
                return optionsResponse.Results;
            }

            return new List<PolygonOptionsContract>();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error fetching options contracts for {underlyingSymbol}");
            return new List<PolygonOptionsContract>();
        }
    }

    public async Task<List<PolygonOptionsContract>> GetOptionsContractsForDateAsync(string underlyingSymbol, DateTime expirationDate)
    {
        return await GetOptionsContractsAsync(underlyingSymbol, expirationDate);
    }

    public async Task<decimal> GetHistoricalPriceAsync(string symbol, DateTime date)
    {
        try
        {
            _logger.LogDebug($"Fetching historical price for {symbol} on {date:yyyy-MM-dd}");

            var apiKey = await GetSecurePolygonApiKeyAsync();
            var dateStr = date.ToString("yyyy-MM-dd");
            var url = $"/v2/aggs/ticker/{symbol}/range/1/day/{dateStr}/{dateStr}?adjusted=true&apikey={apiKey}";

            var response = await _httpClient.GetAsync(url);

            if (!response.IsSuccessStatusCode)
            {
                _logger.LogWarning($"Polygon API returned {response.StatusCode} for historical price request");
                return 0;
            }

            var content = await response.Content.ReadAsStringAsync();
            var historyResponse = JsonConvert.DeserializeObject<PolygonHistoryResponse>(content);

            if (historyResponse?.Results?.Any() == true)
            {
                var result = historyResponse.Results.First();
                var price = (decimal)result.Close;
                _logger.LogDebug($"Historical price for {symbol} on {date:yyyy-MM-dd}: ${price:F2}");
                return price;
            }

            return 0;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error fetching historical price for {symbol} on {date:yyyy-MM-dd}");
            return 0;
        }
    }

    public async Task<bool> IsMarketOpenAsync()
    {
        try
        {
            var apiKey = await GetSecurePolygonApiKeyAsync();
            var url = $"/v1/marketstatus/now?apikey={apiKey}";
            var response = await _httpClient.GetAsync(url);

            if (!response.IsSuccessStatusCode)
            {
                _logger.LogWarning($"Could not get market status: {response.StatusCode}");
                return false;
            }

            var content = await response.Content.ReadAsStringAsync();
            var marketStatus = JsonConvert.DeserializeObject<PolygonMarketStatusResponse>(content);

            // Check if NYSE is open (primary indicator for US equity markets)
            return marketStatus?.Exchanges?.Nyse == "open";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking market status");
            return false;
        }
    }

    private async Task<decimal> GetFallbackVixAsync()
    {
        try
        {
            // If we have a cached value that's not too old, use it
            if (_cachedVix > 0 && DateTime.UtcNow - _lastVixUpdate < TimeSpan.FromHours(1))
            {
                _logger.LogDebug($"Using cached VIX as fallback: {_cachedVix:F2}");
                return _cachedVix;
            }

            // Otherwise, use a reasonable default based on current market conditions
            var fallbackVix = 20.0m; // Reasonable default
            _logger.LogWarning($"Using fallback VIX value: {fallbackVix:F2}");
            return fallbackVix;
        }
        catch
        {
            return 20.0m; // Safe default
        }
    }

    /// <summary>
    /// Determines if a symbol represents an index (which doesn't have bid/ask quotes)
    /// </summary>
    private bool IsIndexSymbol(string symbol)
    {
        if (string.IsNullOrEmpty(symbol))
            return false;

        // Polygon index symbols start with "I:"
        if (symbol.StartsWith("I:", StringComparison.OrdinalIgnoreCase))
            return true;

        // Common index symbols that might not have the "I:" prefix
        var indexSymbols = new[] { "VIX", "SPX", "NDX", "RUT", "DJX", "VIX9D", "VIX3M" };
        return indexSymbols.Contains(symbol, StringComparer.OrdinalIgnoreCase);
    }

    /// <summary>
    /// Gets index price as a quote object using last trade data
    /// Indices don't have bid/ask, so we use the last trade price for both bid and ask
    /// </summary>
    private async Task<PolygonQuote> GetIndexPriceAsQuoteAsync(string symbol)
    {
        try
        {
            _logger.LogDebug($"Getting index price for {symbol} using last trade endpoint");

            var apiKey = await GetSecurePolygonApiKeyAsync();
            var url = $"/v2/last/trade/{symbol}?apikey={apiKey}";
            var response = await _httpClient.GetAsync(url);

            if (!response.IsSuccessStatusCode)
            {
                _logger.LogWarning($"Polygon API returned {response.StatusCode} for {symbol} last trade");
                return new PolygonQuote { Symbol = symbol };
            }

            var content = await response.Content.ReadAsStringAsync();
            var tradeResponse = JsonConvert.DeserializeObject<PolygonVixResponse>(content);

            if (tradeResponse?.Results?.Price > 0)
            {
                var price = (decimal)tradeResponse.Results.Price;
                _logger.LogDebug($"Index {symbol} last trade price: {price:F2}");

                // For indices, we set both bid and ask to the last trade price
                // This provides a usable quote object while being clear it's not real bid/ask
                return new PolygonQuote
                {
                    Symbol = symbol,
                    Bid = price,
                    Ask = price,
                    BidSize = 0, // Indices don't have size
                    AskSize = 0,
                    Timestamp = DateTimeOffset.FromUnixTimeMilliseconds(tradeResponse.Results.Timestamp).DateTime
                };
            }

            _logger.LogWarning($"No valid trade data for index {symbol}");
            return new PolygonQuote { Symbol = symbol };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error fetching index price for {symbol}");
            return new PolygonQuote { Symbol = symbol };
        }
    }

    /// <summary>
    /// Gets comprehensive options chain snapshot with real-time pricing, Greeks, and IV
    /// Perfect for Options Starter subscription with 15-minute delayed data
    /// </summary>
    public async Task<PolygonOptionsChainSnapshot> GetOptionsChainSnapshotAsync(string underlyingSymbol, DateTime? expirationDate = null, decimal? strikePrice = null)
    {
        try
        {
            _logger.LogDebug($"Fetching options chain snapshot for {underlyingSymbol}");

            var apiKey = await GetSecurePolygonApiKeyAsync();
            var url = $"/v3/snapshot/options/{underlyingSymbol}?limit=250&apikey={apiKey}";

            if (expirationDate.HasValue)
            {
                var expDateStr = expirationDate.Value.ToString("yyyy-MM-dd");
                url += $"&expiration_date={expDateStr}";
            }

            if (strikePrice.HasValue)
            {
                url += $"&strike_price={strikePrice.Value}";
            }

            var response = await _httpClient.GetAsync(url);

            if (!response.IsSuccessStatusCode)
            {
                _logger.LogWarning($"Polygon API returned {response.StatusCode} for options chain snapshot");
                return new PolygonOptionsChainSnapshot { UnderlyingSymbol = underlyingSymbol };
            }

            var content = await response.Content.ReadAsStringAsync();
            var chainResponse = JsonConvert.DeserializeObject<PolygonOptionsChainSnapshotResponse>(content);

            if (chainResponse?.Results != null)
            {
                _logger.LogInformation($"Retrieved options chain snapshot with {chainResponse.Results.Count} contracts for {underlyingSymbol}");
                return new PolygonOptionsChainSnapshot
                {
                    UnderlyingSymbol = underlyingSymbol,
                    Options = chainResponse.Results,
                    LastUpdated = DateTime.UtcNow
                };
            }

            return new PolygonOptionsChainSnapshot { UnderlyingSymbol = underlyingSymbol };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error fetching options chain snapshot for {underlyingSymbol}");
            return new PolygonOptionsChainSnapshot { UnderlyingSymbol = underlyingSymbol };
        }
    }

    /// <summary>
    /// Gets individual option contract snapshot with current pricing and Greeks
    /// </summary>
    public async Task<PolygonOptionSnapshot> GetOptionSnapshotAsync(string optionTicker)
    {
        try
        {
            _logger.LogDebug($"Fetching option snapshot for {optionTicker}");

            var apiKey = await GetSecurePolygonApiKeyAsync();
            var url = $"/v3/snapshot/options/{optionTicker}?apikey={apiKey}";

            var response = await _httpClient.GetAsync(url);

            if (!response.IsSuccessStatusCode)
            {
                _logger.LogWarning($"Polygon API returned {response.StatusCode} for option snapshot");
                return new PolygonOptionSnapshot { Ticker = optionTicker };
            }

            var content = await response.Content.ReadAsStringAsync();
            var snapshotResponse = JsonConvert.DeserializeObject<PolygonOptionSnapshotResponse>(content);

            if (snapshotResponse?.Results != null)
            {
                _logger.LogDebug($"Retrieved option snapshot for {optionTicker}");
                return snapshotResponse.Results;
            }

            return new PolygonOptionSnapshot { Ticker = optionTicker };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error fetching option snapshot for {optionTicker}");
            return new PolygonOptionSnapshot { Ticker = optionTicker };
        }
    }

    /// <summary>
    /// Gets option aggregates (OHLC bars) for pricing analysis
    /// </summary>
    public async Task<PolygonOptionAggregates> GetOptionAggregatesAsync(string optionTicker, DateTime fromDate, DateTime toDate, string timespan = "minute")
    {
        try
        {
            _logger.LogDebug($"Fetching option aggregates for {optionTicker}");

            var apiKey = await GetSecurePolygonApiKeyAsync();
            var fromDateStr = fromDate.ToString("yyyy-MM-dd");
            var toDateStr = toDate.ToString("yyyy-MM-dd");
            var url = $"/v2/aggs/ticker/{optionTicker}/range/1/{timespan}/{fromDateStr}/{toDateStr}?adjusted=true&sort=desc&limit=50000&apikey={apiKey}";

            var response = await _httpClient.GetAsync(url);

            if (!response.IsSuccessStatusCode)
            {
                _logger.LogWarning($"Polygon API returned {response.StatusCode} for option aggregates");
                return new PolygonOptionAggregates { Ticker = optionTicker };
            }

            var content = await response.Content.ReadAsStringAsync();
            var aggregatesResponse = JsonConvert.DeserializeObject<PolygonOptionAggregatesResponse>(content);

            if (aggregatesResponse?.Results != null)
            {
                _logger.LogDebug($"Retrieved {aggregatesResponse.Results.Count} aggregates for {optionTicker}");
                return new PolygonOptionAggregates
                {
                    Ticker = optionTicker,
                    Bars = aggregatesResponse.Results,
                    LastUpdated = DateTime.UtcNow
                };
            }

            return new PolygonOptionAggregates { Ticker = optionTicker };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error fetching option aggregates for {optionTicker}");
            return new PolygonOptionAggregates { Ticker = optionTicker };
        }
    }

    /// <summary>
    /// Placeholder methods for quotes and trades (available in higher tiers)
    /// </summary>
    public async Task<List<PolygonOptionQuote>> GetOptionQuotesAsync(string optionTicker, DateTime? date = null)
    {
        // Options Starter doesn't include real-time quotes, but we can get them from snapshots
        try
        {
            var snapshot = await GetOptionSnapshotAsync(optionTicker);
            if (snapshot.LastQuote != null)
            {
                return new List<PolygonOptionQuote> { snapshot.LastQuote };
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error getting option quotes for {optionTicker}");
        }

        return new List<PolygonOptionQuote>();
    }

    public async Task<List<PolygonOptionTrade>> GetOptionTradesAsync(string optionTicker, DateTime? date = null)
    {
        // Options Starter doesn't include trades, but we can get last trade from snapshots
        try
        {
            var snapshot = await GetOptionSnapshotAsync(optionTicker);
            if (snapshot.LastTrade != null)
            {
                return new List<PolygonOptionTrade> { snapshot.LastTrade };
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, $"Error getting option trades for {optionTicker}");
        }

        return new List<PolygonOptionTrade>();
    }
}

// Polygon.io response models
public class PolygonVixResponse
{
    [JsonProperty("results")]
    public PolygonVixResult? Results { get; set; }
}

public class PolygonVixResult
{
    [JsonProperty("p")]
    public double Price { get; set; }
    
    [JsonProperty("t")]
    public long Timestamp { get; set; }
}

public class PolygonHistoryResponse
{
    [JsonProperty("results")]
    public List<PolygonHistoryResult>? Results { get; set; }
}

public class PolygonHistoryResult
{
    [JsonProperty("c")]
    public double Close { get; set; }
    
    [JsonProperty("h")]
    public double High { get; set; }
    
    [JsonProperty("l")]
    public double Low { get; set; }
    
    [JsonProperty("v")]
    public long Volume { get; set; }
    
    [JsonProperty("t")]
    public long Timestamp { get; set; }
}

public class PolygonQuoteResponse
{
    [JsonProperty("results")]
    public PolygonQuoteResult? Results { get; set; }
}

public class PolygonQuoteResult
{
    [JsonProperty("bid")]
    public double Bid { get; set; }
    
    [JsonProperty("ask")]
    public double Ask { get; set; }
    
    [JsonProperty("bidSize")]
    public int BidSize { get; set; }
    
    [JsonProperty("askSize")]
    public int AskSize { get; set; }
    
    [JsonProperty("t")]
    public long Timestamp { get; set; }
}

public class PolygonQuote
{
    public string Symbol { get; set; } = string.Empty;
    public decimal Bid { get; set; }
    public decimal Ask { get; set; }
    public int BidSize { get; set; }
    public int AskSize { get; set; }
    public DateTime Timestamp { get; set; }
    public decimal MidPrice => (Bid + Ask) / 2;
    public decimal Spread => Ask - Bid;
    public decimal SpreadPercentage => MidPrice > 0 ? (Spread / MidPrice) * 100 : 0;
}

// Options-related models
public class PolygonOptionsContractsResponse
{
    [JsonProperty("results")]
    public List<PolygonOptionsContract> Results { get; set; } = new();

    [JsonProperty("status")]
    public string Status { get; set; } = string.Empty;

    [JsonProperty("count")]
    public int Count { get; set; }
}

public class PolygonOptionsContract
{
    [JsonProperty("ticker")]
    public string Ticker { get; set; } = string.Empty;

    [JsonProperty("underlying_ticker")]
    public string UnderlyingTicker { get; set; } = string.Empty;

    [JsonProperty("expiration_date")]
    public string ExpirationDate { get; set; } = string.Empty;

    [JsonProperty("strike_price")]
    public decimal StrikePrice { get; set; }

    [JsonProperty("contract_type")]
    public string ContractType { get; set; } = string.Empty;

    [JsonProperty("shares_per_contract")]
    public int SharesPerContract { get; set; } = 100;

    [JsonProperty("exercise_style")]
    public string ExerciseStyle { get; set; } = string.Empty;

    [JsonProperty("primary_exchange")]
    public string PrimaryExchange { get; set; } = string.Empty;

    // Helper properties
    public DateTime ExpirationDateTime => DateTime.TryParse(ExpirationDate, out var date) ? date : DateTime.MinValue;
    public bool IsCall => ContractType?.ToLower() == "call";
    public bool IsPut => ContractType?.ToLower() == "put";
}

public class PolygonMarketStatusResponse
{
    [JsonProperty("exchanges")]
    public PolygonExchangeStatus? Exchanges { get; set; }

    [JsonProperty("afterHours")]
    public bool AfterHours { get; set; }

    [JsonProperty("earlyHours")]
    public bool EarlyHours { get; set; }
}

public class PolygonExchangeStatus
{
    [JsonProperty("nyse")]
    public string Nyse { get; set; } = string.Empty;

    [JsonProperty("nasdaq")]
    public string Nasdaq { get; set; } = string.Empty;

    [JsonProperty("otc")]
    public string Otc { get; set; } = string.Empty;
}

// Enhanced Options Models for Options Starter Subscription
public class PolygonOptionsChainSnapshot
{
    public string UnderlyingSymbol { get; set; } = string.Empty;
    public List<PolygonOptionSnapshot> Options { get; set; } = new();
    public DateTime LastUpdated { get; set; }
}

public class PolygonOptionsChainSnapshotResponse
{
    [JsonProperty("results")]
    public List<PolygonOptionSnapshot> Results { get; set; } = new();

    [JsonProperty("status")]
    public string Status { get; set; } = string.Empty;

    [JsonProperty("count")]
    public int Count { get; set; }
}

public class PolygonOptionSnapshot
{
    [JsonProperty("ticker")]
    public string Ticker { get; set; } = string.Empty;

    [JsonProperty("break_even_price")]
    public decimal? BreakEvenPrice { get; set; }

    [JsonProperty("day")]
    public PolygonOptionDay? Day { get; set; }

    [JsonProperty("details")]
    public PolygonOptionDetails? Details { get; set; }

    [JsonProperty("fmv")]
    public decimal? FairMarketValue { get; set; }

    [JsonProperty("greeks")]
    public PolygonOptionGreeks? Greeks { get; set; }

    [JsonProperty("implied_volatility")]
    public decimal? ImpliedVolatility { get; set; }

    [JsonProperty("last_quote")]
    public PolygonOptionQuote? LastQuote { get; set; }

    [JsonProperty("last_trade")]
    public PolygonOptionTrade? LastTrade { get; set; }

    [JsonProperty("open_interest")]
    public decimal? OpenInterest { get; set; }

    [JsonProperty("underlying_asset")]
    public PolygonUnderlyingAsset? UnderlyingAsset { get; set; }

    // Helper properties
    public decimal CurrentPrice => LastTrade?.Price ?? LastQuote?.MidPrice ?? 0;
    public decimal Bid => LastQuote?.Bid ?? 0;
    public decimal Ask => LastQuote?.Ask ?? 0;
    public decimal MidPrice => (Bid + Ask) / 2;
    public decimal Volume => Day?.Volume ?? 0;
}

public class PolygonOptionSnapshotResponse
{
    [JsonProperty("results")]
    public PolygonOptionSnapshot Results { get; set; } = new();

    [JsonProperty("status")]
    public string Status { get; set; } = string.Empty;
}

public class PolygonOptionDay
{
    [JsonProperty("change")]
    public decimal? Change { get; set; }

    [JsonProperty("change_percent")]
    public decimal? ChangePercent { get; set; }

    [JsonProperty("close")]
    public decimal? Close { get; set; }

    [JsonProperty("high")]
    public decimal? High { get; set; }

    [JsonProperty("low")]
    public decimal? Low { get; set; }

    [JsonProperty("open")]
    public decimal? Open { get; set; }

    [JsonProperty("previous_close")]
    public decimal? PreviousClose { get; set; }

    [JsonProperty("volume")]
    public decimal? Volume { get; set; }

    [JsonProperty("vwap")]
    public decimal? Vwap { get; set; }
}

public class PolygonOptionDetails
{
    [JsonProperty("contract_type")]
    public string ContractType { get; set; } = string.Empty;

    [JsonProperty("exercise_style")]
    public string ExerciseStyle { get; set; } = string.Empty;

    [JsonProperty("expiration_date")]
    public string ExpirationDate { get; set; } = string.Empty;

    [JsonProperty("shares_per_contract")]
    public int SharesPerContract { get; set; } = 100;

    [JsonProperty("strike_price")]
    public decimal StrikePrice { get; set; }

    [JsonProperty("ticker")]
    public string Ticker { get; set; } = string.Empty;

    [JsonProperty("underlying_ticker")]
    public string UnderlyingTicker { get; set; } = string.Empty;

    // Helper properties
    public DateTime ExpirationDateTime => DateTime.TryParse(ExpirationDate, out var date) ? date : DateTime.MinValue;
    public bool IsCall => ContractType?.ToLower() == "call";
    public bool IsPut => ContractType?.ToLower() == "put";
    public int DaysToExpiration => (ExpirationDateTime.Date - DateTime.Today).Days;
    public bool IsZeroDte => DaysToExpiration == 0;
}

public class PolygonOptionGreeks
{
    [JsonProperty("delta")]
    public decimal? Delta { get; set; }

    [JsonProperty("gamma")]
    public decimal? Gamma { get; set; }

    [JsonProperty("theta")]
    public decimal? Theta { get; set; }

    [JsonProperty("vega")]
    public decimal? Vega { get; set; }
}

public class PolygonOptionQuote
{
    [JsonProperty("ask")]
    public decimal Ask { get; set; }

    [JsonProperty("ask_size")]
    public int AskSize { get; set; }

    [JsonProperty("bid")]
    public decimal Bid { get; set; }

    [JsonProperty("bid_size")]
    public int BidSize { get; set; }

    [JsonProperty("exchange")]
    public int Exchange { get; set; }

    [JsonProperty("last_updated")]
    public long LastUpdated { get; set; }

    [JsonProperty("timeframe")]
    public string Timeframe { get; set; } = string.Empty;

    // Helper properties
    public decimal MidPrice => (Bid + Ask) / 2;
    public decimal Spread => Ask - Bid;
    public decimal SpreadPercentage => MidPrice > 0 ? (Spread / MidPrice) * 100 : 0;
    public DateTime LastUpdatedDateTime => DateTimeOffset.FromUnixTimeMilliseconds(LastUpdated).DateTime;
}

public class PolygonOptionTrade
{
    [JsonProperty("conditions")]
    public List<int> Conditions { get; set; } = new();

    [JsonProperty("exchange")]
    public int Exchange { get; set; }

    [JsonProperty("price")]
    public decimal Price { get; set; }

    [JsonProperty("sip_timestamp")]
    public long SipTimestamp { get; set; }

    [JsonProperty("size")]
    public int Size { get; set; }

    [JsonProperty("timeframe")]
    public string Timeframe { get; set; } = string.Empty;

    // Helper properties
    public DateTime TradeDateTime => DateTimeOffset.FromUnixTimeMilliseconds(SipTimestamp / 1000000).DateTime;
}

public class PolygonUnderlyingAsset
{
    [JsonProperty("change_to_break_even")]
    public decimal? ChangeToBreakEven { get; set; }

    [JsonProperty("last_updated")]
    public long LastUpdated { get; set; }

    [JsonProperty("market_status")]
    public string MarketStatus { get; set; } = string.Empty;

    [JsonProperty("name")]
    public string Name { get; set; } = string.Empty;

    [JsonProperty("ticker")]
    public string Ticker { get; set; } = string.Empty;

    [JsonProperty("type")]
    public string Type { get; set; } = string.Empty;

    [JsonProperty("url")]
    public string Url { get; set; } = string.Empty;

    [JsonProperty("value")]
    public decimal? Value { get; set; }

    // Helper properties
    public DateTime LastUpdatedDateTime => DateTimeOffset.FromUnixTimeMilliseconds(LastUpdated).DateTime;
}

public class PolygonOptionAggregates
{
    public string Ticker { get; set; } = string.Empty;
    public List<PolygonOptionBar> Bars { get; set; } = new();
    public DateTime LastUpdated { get; set; }
}

public class PolygonOptionAggregatesResponse
{
    [JsonProperty("results")]
    public List<PolygonOptionBar> Results { get; set; } = new();

    [JsonProperty("status")]
    public string Status { get; set; } = string.Empty;

    [JsonProperty("count")]
    public int Count { get; set; }
}

public class PolygonOptionBar
{
    [JsonProperty("c")]
    public decimal Close { get; set; }

    [JsonProperty("h")]
    public decimal High { get; set; }

    [JsonProperty("l")]
    public decimal Low { get; set; }

    [JsonProperty("n")]
    public int NumberOfTransactions { get; set; }

    [JsonProperty("o")]
    public decimal Open { get; set; }

    [JsonProperty("t")]
    public long Timestamp { get; set; }

    [JsonProperty("v")]
    public decimal Volume { get; set; }

    [JsonProperty("vw")]
    public decimal VolumeWeightedAveragePrice { get; set; }

    // Helper properties
    public DateTime DateTime => DateTimeOffset.FromUnixTimeMilliseconds(Timestamp).DateTime;
    public decimal Range => High - Low;
    public decimal BodySize => Math.Abs(Close - Open);
    public bool IsBullish => Close > Open;
}
